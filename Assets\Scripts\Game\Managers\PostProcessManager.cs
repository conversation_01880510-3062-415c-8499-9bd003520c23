using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering;
using SmartVertex.Tools;
using UnityEngine.Rendering.PostProcessing;

namespace Game.Managers
{
    /// <summary>
    /// Manages post-processing volume profiles using Addressables with caching for instant profile switching.
    /// Handles smooth transitions between different post-processing effects.
    /// </summary>
    public class PostProcessManager : Singleton<PostProcessManager>
    {
        #region Serialized Fields

        [Header("Volume Settings")]
        [SerializeField] private PostProcessVolume globalVolume;
        [SerializeField] private bool autoFindGlobalVolume = true;
        
        [Header("Transition Settings")]
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        
        [Header("Cache Settings")]
        [SerializeField] private bool preloadProfilesOnStart = true;
        [SerializeField] private List<string> profileAddressesToPreload = new List<string>();

        #endregion

        #region Private Fields

        private readonly Dictionary<string, PostProcessProfile> profileCache = new Dictionary<string, PostProcessProfile>();
        private readonly AddressablesHelper addressablesHelper = new AddressablesHelper();
        
        private PostProcessProfile originalProfile;
        private Coroutine currentTransition;
        private bool isInitialized = false;

        #endregion

        #region Properties

        /// <summary>
        /// Gets the current global volume being managed.
        /// </summary>
        public Volume GlobalVolume => globalVolume;

        /// <summary>
        /// Gets whether the manager is properly initialized.
        /// </summary>
        public bool IsInitialized => isInitialized;

        /// <summary>
        /// Gets the number of cached profiles.
        /// </summary>
        public int CachedProfileCount => profileCache.Count;

        #endregion

        #region Unity Lifecycle

        protected override void Awake()
        {
            base.Awake();
            Initialize();
        }

        private async void Start()
        {
            if (preloadProfilesOnStart && profileAddressesToPreload.Count > 0)
            {
                await PreloadProfilesAsync(profileAddressesToPreload);
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initializes the PostProcessManager by finding or validating the global volume.
        /// </summary>
        private void Initialize()
        {
            try
            {
                // Auto-find global volume if not assigned
                if (globalVolume == null && autoFindGlobalVolume)
                {
                    globalVolume = FindGlobalVolume();
                }

                if (globalVolume == null)
                {
                    Debug.LogError("[PostProcessManager] No global volume found or assigned. Please assign a global Volume component.");
                    return;
                }

                // Store the original profile for restoration if needed
                originalProfile = globalVolume.profile;
                isInitialized = true;

                Debug.Log($"[PostProcessManager] Initialized successfully with global volume: {globalVolume.name}");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[PostProcessManager] Failed to initialize: {ex.Message}");
            }
        }

        /// <summary>
        /// Finds the first global Volume component in the scene.
        /// </summary>
        /// <returns>The global Volume component, or null if not found.</returns>
        private PostProcessVolume FindGlobalVolume()
        {
            var volumes = FindObjectsByType<PostProcessVolume>(FindObjectsSortMode.None);
            foreach (var volume in volumes)
            {
                if (volume.isGlobal)
                {
                    return volume;
                }
            }
            return null;
        }

        #endregion

        #region Profile Management

        /// <summary>
        /// Sets a post-processing profile with optional intensity and transition duration.
        /// </summary>
        /// <param name="profileAddress">The Addressable address of the volume profile.</param>
        /// <param name="intensity">The intensity/weight of the effect (0.0 to 1.0).</param>
        /// <param name="transitionDuration">The duration of the transition in seconds.</param>
        /// <returns>A coroutine that can be yielded.</returns>
        public IEnumerator SetProfileAsync(string profileAddress, float intensity = 1.0f, float transitionDuration = 0.5f)
        {
            if (!isInitialized)
            {
                Debug.LogError("[PostProcessManager] Manager not initialized. Cannot set profile.");
                yield break;
            }

            if (string.IsNullOrEmpty(profileAddress))
            {
                Debug.LogError("[PostProcessManager] Profile address is null or empty.");
                yield break;
            }

            // Clamp intensity to valid range
            intensity = Mathf.Clamp01(intensity);

            // Get or load the profile
            postproce targetProfile = null;
            if (profileCache.ContainsKey(profileAddress))
            {
                targetProfile = profileCache[profileAddress];
            }
            else
            {
                yield return LoadProfileAsync(profileAddress, (profile) => targetProfile = profile);
            }

            if (targetProfile == null)
            {
                Debug.LogError($"[PostProcessManager] Failed to load profile: {profileAddress}");
                yield break;
            }

            // Apply the profile with transition
            yield return ApplyProfileWithTransition(targetProfile, intensity, transitionDuration);
        }

        /// <summary>
        /// Loads a volume profile from Addressables and caches it.
        /// </summary>
        /// <param name="profileAddress">The Addressable address of the volume profile.</param>
        /// <param name="onComplete">Callback when loading is complete.</param>
        /// <returns>A coroutine that can be yielded.</returns>
        private IEnumerator LoadProfileAsync(string profileAddress, Action<VolumeProfile> onComplete)
        {
            bool loadComplete = false;
            VolumeProfile loadedProfile = null;

            addressablesHelper.LoadAssetAsync<VolumeProfile>(profileAddress, 
                (profile) => loadedProfile = profile,
                (result) => loadComplete = true);

            // Wait for loading to complete
            yield return new WaitUntil(() => loadComplete);

            if (loadedProfile != null)
            {
                profileCache[profileAddress] = loadedProfile;
                Debug.Log($"[PostProcessManager] Successfully loaded and cached profile: {profileAddress}");
            }
            else
            {
                Debug.LogError($"[PostProcessManager] Failed to load profile from address: {profileAddress}");
            }

            onComplete?.Invoke(loadedProfile);
        }

        /// <summary>
        /// Applies a volume profile with smooth transition.
        /// </summary>
        /// <param name="targetProfile">The target volume profile to apply.</param>
        /// <param name="intensity">The final intensity of the effect.</param>
        /// <param name="transitionDuration">The duration of the transition.</param>
        /// <returns>A coroutine that can be yielded.</returns>
        private IEnumerator ApplyProfileWithTransition(VolumeProfile targetProfile, float intensity, float transitionDuration)
        {
            // Stop any existing transition
            if (currentTransition != null)
            {
                StopCoroutine(currentTransition);
            }

            // If no transition duration, apply immediately
            if (transitionDuration <= 0f)
            {
                globalVolume.profile = targetProfile;
                globalVolume.weight = intensity;
                yield break;
            }

            // Store initial values
            var initialProfile = globalVolume.profile;
            var initialWeight = globalVolume.weight;

            // Perform smooth transition
            float elapsedTime = 0f;
            while (elapsedTime < transitionDuration)
            {
                elapsedTime += Time.deltaTime;
                float normalizedTime = elapsedTime / transitionDuration;
                float curveValue = transitionCurve.Evaluate(normalizedTime);

                // Interpolate weight
                globalVolume.weight = Mathf.Lerp(initialWeight, intensity, curveValue);

                // Switch profile at halfway point for smoother transition
                if (normalizedTime >= 0.5f && globalVolume.profile != targetProfile)
                {
                    globalVolume.profile = targetProfile;
                }

                yield return null;
            }

            // Ensure final values are set
            globalVolume.profile = targetProfile;
            globalVolume.weight = intensity;

            Debug.Log($"[PostProcessManager] Successfully applied profile with intensity {intensity:F2}");
        }

        #endregion

        #region Caching

        /// <summary>
        /// Preloads multiple volume profiles for instant access.
        /// </summary>
        /// <param name="profileAddresses">List of Addressable addresses to preload.</param>
        /// <returns>An awaitable task.</returns>
        public async Awaitable PreloadProfilesAsync(List<string> profileAddresses)
        {
            if (profileAddresses == null || profileAddresses.Count == 0)
            {
                Debug.LogWarning("[PostProcessManager] No profile addresses provided for preloading.");
                return;
            }

            Debug.Log($"[PostProcessManager] Starting preload of {profileAddresses.Count} profiles...");

            int loadedCount = 0;
            foreach (var address in profileAddresses)
            {
                if (!profileCache.ContainsKey(address))
                {
                    var profile = await addressablesHelper.LoadAssetAsync<VolumeProfile>(address);
                    if (profile != null)
                    {
                        profileCache[address] = profile;
                        loadedCount++;
                    }
                    else
                    {
                        Debug.LogError($"[PostProcessManager] Failed to preload profile: {address}");
                    }
                }
            }

            Debug.Log($"[PostProcessManager] Preloading complete. Loaded {loadedCount} new profiles. Total cached: {profileCache.Count}");
        }

        /// <summary>
        /// Checks if a profile is already cached.
        /// </summary>
        /// <param name="profileAddress">The Addressable address to check.</param>
        /// <returns>True if the profile is cached, false otherwise.</returns>
        public bool IsProfileCached(string profileAddress)
        {
            return profileCache.ContainsKey(profileAddress);
        }

        /// <summary>
        /// Clears the profile cache and releases Addressable handles.
        /// </summary>
        public void ClearCache()
        {
            Debug.Log($"[PostProcessManager] Clearing cache of {profileCache.Count} profiles...");
            profileCache.Clear();
            addressablesHelper.ReleaseAll();
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Restores the original volume profile that was active when the manager was initialized.
        /// </summary>
        /// <param name="transitionDuration">The duration of the transition back to original.</param>
        /// <returns>A coroutine that can be yielded.</returns>
        public IEnumerator RestoreOriginalProfile(float transitionDuration = 0.5f)
        {
            if (originalProfile != null)
            {
                yield return ApplyProfileWithTransition(originalProfile, 1.0f, transitionDuration);
                Debug.Log("[PostProcessManager] Restored original profile.");
            }
            else
            {
                Debug.LogWarning("[PostProcessManager] No original profile to restore.");
            }
        }

        /// <summary>
        /// Sets the global volume weight without changing the profile.
        /// </summary>
        /// <param name="weight">The weight value (0.0 to 1.0).</param>
        /// <param name="transitionDuration">The duration of the weight transition.</param>
        /// <returns>A coroutine that can be yielded.</returns>
        public IEnumerator SetVolumeWeight(float weight, float transitionDuration = 0.5f)
        {
            if (!isInitialized)
            {
                Debug.LogError("[PostProcessManager] Manager not initialized. Cannot set volume weight.");
                yield break;
            }

            weight = Mathf.Clamp01(weight);

            if (transitionDuration <= 0f)
            {
                globalVolume.weight = weight;
                yield break;
            }

            float initialWeight = globalVolume.weight;
            float elapsedTime = 0f;

            while (elapsedTime < transitionDuration)
            {
                elapsedTime += Time.deltaTime;
                float normalizedTime = elapsedTime / transitionDuration;
                float curveValue = transitionCurve.Evaluate(normalizedTime);

                globalVolume.weight = Mathf.Lerp(initialWeight, weight, curveValue);
                yield return null;
            }

            globalVolume.weight = weight;
        }

        #endregion

        #region Cleanup

        protected override void OnDestroy()
        {
            base.OnDestroy();
            
            // Stop any running transitions
            if (currentTransition != null)
            {
                StopCoroutine(currentTransition);
            }
            
            // Clear cache and release resources
            ClearCache();
        }

        #endregion
    }
}
